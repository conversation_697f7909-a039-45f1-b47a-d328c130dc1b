#!/bin/bash
# Cloudflare Pages 自动部署脚本
# 用于部署Matrix服务栈外部重定向页面

set -e

# 全局变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BUILD_DIR="$SCRIPT_DIR/pages-build"
PAGES_DIR="$BUILD_DIR/public"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 错误退出
error_exit() {
    log_error "$1"
    exit 1
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    # 检查Node.js和npm
    if ! command -v node &> /dev/null; then
        error_exit "Node.js 未安装，请先安装 Node.js"
    fi
    
    if ! command -v npm &> /dev/null; then
        error_exit "npm 未安装，请先安装 npm"
    fi
    
    # 检查wrangler CLI
    if ! command -v wrangler &> /dev/null; then
        log_warning "Wrangler CLI 未安装，正在安装..."
        npm install -g wrangler
    fi
    
    log_success "依赖检查完成"
}

# 收集配置
collect_config() {
    log_info "收集部署配置..."
    
    echo "=== Cloudflare Pages 部署配置 ==="
    
    # 域名配置
    read -p "请输入主域名 (如: example.com): " DOMAIN
    [[ -z "$DOMAIN" ]] && error_exit "域名不能为空"
    
    read -p "请输入内部服务器的自定义HTTPS端口 [默认: 8443]: " CUSTOM_HTTPS_PORT
    CUSTOM_HTTPS_PORT=${CUSTOM_HTTPS_PORT:-8443}
    
    # 子域名配置
    read -p "Synapse子域名 [默认: matrix]: " MATRIX_SUBDOMAIN
    MATRIX_SUBDOMAIN=${MATRIX_SUBDOMAIN:-matrix}
    
    read -p "Element Web子域名 [默认: chat]: " ELEMENT_SUBDOMAIN
    ELEMENT_SUBDOMAIN=${ELEMENT_SUBDOMAIN:-chat}
    
    read -p "MAS子域名 [默认: account]: " MAS_SUBDOMAIN
    MAS_SUBDOMAIN=${MAS_SUBDOMAIN:-account}
    
    read -p "Matrix RTC子域名 [默认: mrtc]: " MRTC_SUBDOMAIN
    MRTC_SUBDOMAIN=${MRTC_SUBDOMAIN:-mrtc}
    
    # Cloudflare配置
    read -p "请输入Cloudflare账户ID: " CF_ACCOUNT_ID
    [[ -z "$CF_ACCOUNT_ID" ]] && error_exit "Cloudflare账户ID不能为空"
    
    read -p "请输入项目名称 [默认: matrix-redirect]: " PROJECT_NAME
    PROJECT_NAME=${PROJECT_NAME:-matrix-redirect}
    
    # 内部服务器IP（用于重定向）
    read -p "请输入内部服务器的公网IP或域名: " INTERNAL_SERVER
    [[ -z "$INTERNAL_SERVER" ]] && error_exit "内部服务器地址不能为空"
    
    log_success "配置收集完成"
}

# 创建构建目录
create_build_structure() {
    log_info "创建构建目录结构..."
    
    # 清理并创建目录
    rm -rf "$BUILD_DIR"
    mkdir -p "$PAGES_DIR"
    
    # 创建package.json
    cat > "$BUILD_DIR/package.json" << EOF
{
  "name": "matrix-redirect-pages",
  "version": "1.0.0",
  "description": "Matrix服务栈外部重定向页面",
  "scripts": {
    "build": "echo 'Static pages, no build needed'",
    "deploy": "wrangler pages deploy public --project-name=$PROJECT_NAME"
  },
  "devDependencies": {
    "wrangler": "^3.0.0"
  }
}
EOF
    
    # 创建wrangler.toml
    cat > "$BUILD_DIR/wrangler.toml" << EOF
name = "$PROJECT_NAME"
compatibility_date = "2023-12-01"

[env.production]
account_id = "$CF_ACCOUNT_ID"

[[env.production.routes]]
pattern = "$MATRIX_SUBDOMAIN.$DOMAIN/*"
zone_name = "$DOMAIN"

[[env.production.routes]]
pattern = "$ELEMENT_SUBDOMAIN.$DOMAIN/*"
zone_name = "$DOMAIN"

[[env.production.routes]]
pattern = "$MAS_SUBDOMAIN.$DOMAIN/*"
zone_name = "$DOMAIN"

[[env.production.routes]]
pattern = "$MRTC_SUBDOMAIN.$DOMAIN/*"
zone_name = "$DOMAIN"
EOF
    
    log_success "构建目录创建完成"
}

# 生成重定向页面
generate_redirect_pages() {
    log_info "生成重定向页面..."
    
    # 创建主页面
    cat > "$PAGES_DIR/index.html" << EOF
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Matrix服务 - $DOMAIN</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .service-link {
            display: block;
            padding: 15px 20px;
            margin: 10px 0;
            background: #0066cc;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.3s;
        }
        .service-link:hover {
            background: #0052a3;
        }
        .info {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Matrix服务 - $DOMAIN</h1>
        <div class="info">
            <p>欢迎访问Matrix服务！请选择您要访问的服务：</p>
        </div>
        
        <a href="https://$MATRIX_SUBDOMAIN.$DOMAIN:$CUSTOM_HTTPS_PORT" class="service-link">
            🏠 Matrix服务器 (Synapse)
        </a>
        
        <a href="https://$ELEMENT_SUBDOMAIN.$DOMAIN:$CUSTOM_HTTPS_PORT" class="service-link">
            💬 聊天客户端 (Element Web)
        </a>
        
        <a href="https://$MAS_SUBDOMAIN.$DOMAIN:$CUSTOM_HTTPS_PORT" class="service-link">
            👤 账户管理 (MAS)
        </a>
        
        <a href="https://$MRTC_SUBDOMAIN.$DOMAIN:$CUSTOM_HTTPS_PORT" class="service-link">
            📞 音视频通话 (Matrix RTC)
        </a>
        
        <div class="info">
            <p><strong>注意：</strong>服务运行在自定义端口 $CUSTOM_HTTPS_PORT 上</p>
            <p>如果无法访问，请检查内部服务器是否正常运行</p>
        </div>
    </div>
</body>
</html>
EOF
    
    # 创建_redirects文件（Cloudflare Pages重定向规则）
    cat > "$PAGES_DIR/_redirects" << EOF
# Matrix服务重定向规则
# Synapse (Matrix服务器)
https://$MATRIX_SUBDOMAIN.$DOMAIN/* https://$INTERNAL_SERVER:$CUSTOM_HTTPS_PORT/:splat 301!
http://$MATRIX_SUBDOMAIN.$DOMAIN/* https://$INTERNAL_SERVER:$CUSTOM_HTTPS_PORT/:splat 301!

# Element Web (聊天客户端)
https://$ELEMENT_SUBDOMAIN.$DOMAIN/* https://$INTERNAL_SERVER:$CUSTOM_HTTPS_PORT/:splat 301!
http://$ELEMENT_SUBDOMAIN.$DOMAIN/* https://$INTERNAL_SERVER:$CUSTOM_HTTPS_PORT/:splat 301!

# Matrix Authentication Service
https://$MAS_SUBDOMAIN.$DOMAIN/* https://$INTERNAL_SERVER:$CUSTOM_HTTPS_PORT/:splat 301!
http://$MAS_SUBDOMAIN.$DOMAIN/* https://$INTERNAL_SERVER:$CUSTOM_HTTPS_PORT/:splat 301!

# Matrix RTC Backend
https://$MRTC_SUBDOMAIN.$DOMAIN/* https://$INTERNAL_SERVER:$CUSTOM_HTTPS_PORT/:splat 301!
http://$MRTC_SUBDOMAIN.$DOMAIN/* https://$INTERNAL_SERVER:$CUSTOM_HTTPS_PORT/:splat 301!

# 默认重定向到主页
/* /index.html 200
EOF
    
    # 创建.well-known目录和文件
    mkdir -p "$PAGES_DIR/.well-known"
    
    # Matrix服务器发现文件
    cat > "$PAGES_DIR/.well-known/matrix/server" << EOF
{
    "m.server": "$MATRIX_SUBDOMAIN.$DOMAIN:$CUSTOM_HTTPS_PORT"
}
EOF
    
    cat > "$PAGES_DIR/.well-known/matrix/client" << EOF
{
    "m.homeserver": {
        "base_url": "https://$MATRIX_SUBDOMAIN.$DOMAIN:$CUSTOM_HTTPS_PORT"
    },
    "m.identity_server": {
        "base_url": "https://$MAS_SUBDOMAIN.$DOMAIN:$CUSTOM_HTTPS_PORT"
    }
}
EOF
    
    log_success "重定向页面生成完成"
}

# 部署到Cloudflare Pages
deploy_to_pages() {
    log_info "部署到Cloudflare Pages..."
    
    cd "$BUILD_DIR"
    
    # 安装依赖
    npm install
    
    # 登录Cloudflare（如果需要）
    if ! wrangler whoami &>/dev/null; then
        log_info "请登录Cloudflare账户..."
        wrangler login
    fi
    
    # 创建Pages项目（如果不存在）
    log_info "创建/更新Pages项目..."
    wrangler pages project create "$PROJECT_NAME" --compatibility-date=2023-12-01 || true
    
    # 部署页面
    log_info "正在部署页面..."
    wrangler pages deploy public --project-name="$PROJECT_NAME"
    
    log_success "部署完成！"
}

# 显示部署信息
show_deployment_info() {
    log_success "=== 部署完成 ==="
    echo
    echo "项目名称: $PROJECT_NAME"
    echo "主域名: $DOMAIN"
    echo "内部服务器: $INTERNAL_SERVER:$CUSTOM_HTTPS_PORT"
    echo
    echo "服务访问地址："
    echo "  Matrix服务器: https://$MATRIX_SUBDOMAIN.$DOMAIN"
    echo "  聊天客户端: https://$ELEMENT_SUBDOMAIN.$DOMAIN"
    echo "  账户管理: https://$MAS_SUBDOMAIN.$DOMAIN"
    echo "  音视频通话: https://$MRTC_SUBDOMAIN.$DOMAIN"
    echo
    echo "Cloudflare Pages URL: https://$PROJECT_NAME.pages.dev"
    echo
    echo "后续步骤："
    echo "1. 在Cloudflare DNS中添加CNAME记录，将子域名指向 $PROJECT_NAME.pages.dev"
    echo "2. 在Cloudflare Pages设置中配置自定义域名"
    echo "3. 确保内部服务器 $INTERNAL_SERVER:$CUSTOM_HTTPS_PORT 正常运行"
}

# 主函数
main() {
    echo "=== Cloudflare Pages 自动部署脚本 ==="
    echo "用于部署Matrix服务栈外部重定向页面"
    echo
    
    check_dependencies
    collect_config
    create_build_structure
    generate_redirect_pages
    deploy_to_pages
    show_deployment_info
    
    log_success "所有操作完成！"
}

# 清理函数
cleanup() {
    log_info "清理临时文件..."
    # 保留构建目录以便调试，用户可手动删除
    log_info "构建文件保存在: $BUILD_DIR"
}

# 信号处理
trap cleanup EXIT

# 执行主函数
main "$@"
