# Cloudflare Pages 小白部署指南
**目标：** 为Matrix服务创建主域名导航页面和well-known配置  
**适用人群：** 完全没有经验的小白用户  
**预计时间：** 30分钟

## 📋 准备工作

### 你需要有：
1. **一个域名**（如：example.com）
2. **Cloudflare免费账户**
3. **域名已托管在Cloudflare**（DNS由Cloudflare管理）
4. **内部服务器的公网IP地址**

### 如果你还没有Cloudflare账户：
1. 访问 https://cloudflare.com
2. 点击"注册"创建免费账户
3. 添加你的域名到Cloudflare
4. 按照提示修改域名的DNS服务器到Cloudflare

---

## 🎯 第一步：创建项目文件

### 1.1 在电脑上创建文件夹
```
在桌面创建文件夹：matrix-pages
在matrix-pages里面创建文件夹：public
```

最终结构：
```
桌面/
└── matrix-pages/
    └── public/
```

### 1.2 创建主页文件
在 `public` 文件夹里创建文件：`index.html`

**文件内容：**（复制粘贴下面的代码，记得修改域名）
```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Matrix服务 - 你的域名.com</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
            line-height: 1.6;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .service-link {
            display: block;
            padding: 15px 20px;
            margin: 15px 0;
            background: #0066cc;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            transition: background 0.3s;
            font-size: 16px;
        }
        .service-link:hover {
            background: #0052a3;
        }
        .info {
            background: #e7f3ff;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #0066cc;
        }
        .status {
            background: #f0f9ff;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: center;
        }
        .emoji {
            font-size: 24px;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏠 Matrix服务中心</h1>
        
        <div class="info">
            <p><strong>欢迎使用Matrix服务！</strong></p>
            <p>这是您的私人Matrix服务器，提供安全的即时通讯和协作功能。</p>
        </div>
        
        <div class="status">
            <p><strong>服务状态：</strong> 🟢 正常运行</p>
            <p><strong>服务端口：</strong> 8443</p>
        </div>
        
        <h2>📱 可用服务</h2>
        
        <a href="https://matrix.你的域名.com:8443" class="service-link">
            <span class="emoji">🏠</span>Matrix服务器 (Synapse)
            <br><small>Matrix协议的核心服务器</small>
        </a>
        
        <a href="https://chat.你的域名.com:8443" class="service-link">
            <span class="emoji">💬</span>聊天客户端 (Element Web)
            <br><small>网页版聊天界面</small>
        </a>
        
        <a href="https://account.你的域名.com:8443" class="service-link">
            <span class="emoji">👤</span>账户管理 (MAS)
            <br><small>用户账户和认证管理</small>
        </a>
        
        <a href="https://mrtc.你的域名.com:8443" class="service-link">
            <span class="emoji">📞</span>音视频通话 (Matrix RTC)
            <br><small>语音和视频通话功能</small>
        </a>
        
        <div class="info">
            <h3>📱 如何连接？</h3>
            <p><strong>使用Matrix客户端：</strong></p>
            <ol>
                <li>下载Element客户端（手机或电脑）</li>
                <li>选择"自定义服务器"</li>
                <li>输入服务器地址：<code>你的域名.com</code></li>
                <li>客户端会自动发现服务器配置</li>
                <li>使用您的账户登录</li>
            </ol>
        </div>
        
        <div class="info">
            <h3>❓ 需要帮助？</h3>
            <p>如果遇到连接问题，请检查：</p>
            <ul>
                <li>内部服务器是否正常运行</li>
                <li>端口8443是否开放</li>
                <li>防火墙设置是否正确</li>
            </ul>
        </div>
    </div>
</body>
</html>
```

**⚠️ 重要：** 把上面代码中的 `你的域名.com` 全部替换成你的真实域名！

### 1.3 创建well-known文件夹
在 `public` 文件夹里创建文件夹：`.well-known`
在 `.well-known` 文件夹里创建文件夹：`matrix`

最终结构：
```
public/
├── index.html
└── .well-known/
    └── matrix/
```

### 1.4 创建Matrix发现文件

#### 文件1：server
在 `public/.well-known/matrix/` 里创建文件：`server`（没有扩展名）

**文件内容：**（记得修改域名）
```json
{
    "m.server": "matrix.你的域名.com:8443"
}
```

#### 文件2：client  
在 `public/.well-known/matrix/` 里创建文件：`client`（没有扩展名）

**文件内容：**（记得修改域名）
```json
{
    "m.homeserver": {
        "base_url": "https://matrix.你的域名.com:8443"
    },
    "m.identity_server": {
        "base_url": "https://account.你的域名.com:8443"
    }
}
```

### 1.5 最终文件结构检查
确保你的文件夹结构是这样的：
```
matrix-pages/
└── public/
    ├── index.html
    └── .well-known/
        └── matrix/
            ├── server
            └── client
```

---

## 🚀 第二步：部署到Cloudflare Pages

### 2.1 登录Cloudflare
1. 打开浏览器，访问 https://dash.cloudflare.com
2. 用你的账户登录

### 2.2 进入Pages服务
1. 在左侧菜单找到 "Workers & Pages"
2. 点击进入
3. 点击 "Create application"
4. 选择 "Pages" 标签
5. 点击 "Upload assets"

### 2.3 创建项目
1. **项目名称：** 输入 `matrix-redirect`（或你喜欢的名字）
2. **上传文件：** 
   - 点击 "Select from computer"
   - 选择你的 `public` 文件夹里的所有文件
   - 确保包括 `index.html` 和 `.well-known` 文件夹
3. 点击 "Deploy site"

### 2.4 等待部署完成
- 部署通常需要1-2分钟
- 完成后你会看到一个网址，类似：`https://matrix-redirect.pages.dev`

---

## 🌐 第三步：配置DNS

### 3.1 设置主域名
1. 在Cloudflare控制台，点击你的域名
2. 进入 "DNS" → "Records"
3. 添加记录：
   - **类型：** CNAME
   - **名称：** @ （表示主域名）
   - **目标：** matrix-redirect.pages.dev
   - **代理状态：** 已代理（橙色云朵）

### 3.2 设置子域名
为每个Matrix服务添加DNS记录，都指向你的内部服务器IP：

**记录1：Matrix服务器**
- **类型：** A
- **名称：** matrix
- **IPv4地址：** 你的内部服务器公网IP
- **代理状态：** 仅DNS（灰色云朵）

**记录2：聊天客户端**
- **类型：** A  
- **名称：** chat
- **IPv4地址：** 你的内部服务器公网IP
- **代理状态：** 仅DNS（灰色云朵）

**记录3：账户管理**
- **类型：** A
- **名称：** account  
- **IPv4地址：** 你的内部服务器公网IP
- **代理状态：** 仅DNS（灰色云朵）

**记录4：音视频通话**
- **类型：** A
- **名称：** mrtc
- **IPv4地址：** 你的内部服务器公网IP  
- **代理状态：** 仅DNS（灰色云朵）

### 3.3 配置自定义域名
1. 回到 Cloudflare Pages 项目页面
2. 点击 "Custom domains"
3. 点击 "Set up a custom domain"
4. 输入你的主域名：`你的域名.com`
5. 点击 "Continue"
6. 等待SSL证书自动配置（通常几分钟）

---

## ✅ 第四步：测试验证

### 4.1 测试主域名
在浏览器访问：`https://你的域名.com`
- 应该看到Matrix服务导航页面
- 页面显示正常，没有错误

### 4.2 测试Matrix发现
在浏览器访问：`https://你的域名.com/.well-known/matrix/server`
- 应该显示JSON格式的服务器信息
- 内容类似：`{"m.server": "matrix.你的域名.com:8443"}`

在浏览器访问：`https://你的域名.com/.well-known/matrix/client`
- 应该显示JSON格式的客户端配置
- 包含homeserver和identity_server信息

### 4.3 测试子域名解析
使用命令行或在线工具测试DNS解析：
```
nslookup matrix.你的域名.com
nslookup chat.你的域名.com
nslookup account.你的域名.com
nslookup mrtc.你的域名.com
```
都应该返回你的内部服务器IP地址。

---

## 🎉 完成！

现在你的Matrix服务已经可以通过主域名访问了！

### 用户如何使用：
1. **访问服务导航：** https://你的域名.com
2. **Matrix客户端连接：** 输入 `你的域名.com` 作为服务器地址
3. **直接访问服务：** https://matrix.你的域名.com:8443

### 如果遇到问题：
1. **DNS未生效：** 等待几分钟到几小时
2. **SSL证书问题：** 在Cloudflare Pages中重新配置自定义域名
3. **well-known文件访问失败：** 检查文件格式和路径是否正确
4. **Matrix客户端无法连接：** 确认内部服务器正常运行且端口开放

恭喜！你已经成功部署了Matrix服务的外部访问入口！🎊

---

## 📝 附录：常见问题解答

### Q1：为什么主域名用Cloudflare Pages，子域名直接指向IP？
**A：** 这样设计的好处：
- 主域名提供友好的导航页面和Matrix发现配置
- 子域名直接访问内部服务器，减少跳转，速度更快
- 节省Cloudflare Pages的流量配额

### Q2：如果内部服务器IP变了怎么办？
**A：** 只需要在Cloudflare DNS中修改A记录：
1. 进入DNS管理
2. 找到matrix、chat、account、mrtc这4个A记录
3. 修改IP地址为新的内部服务器IP
4. 等待DNS生效（通常几分钟）

### Q3：可以使用其他端口吗？
**A：** 可以，需要修改两个地方：
1. `index.html` 中的链接地址
2. `.well-known/matrix/` 文件中的端口号
3. 重新上传到Cloudflare Pages

### Q4：如何更新页面内容？
**A：**
1. 修改本地的文件
2. 在Cloudflare Pages项目中点击"Create deployment"
3. 重新上传修改后的文件

### Q5：Matrix客户端连接失败怎么办？
**A：** 检查顺序：
1. 访问 `https://你的域名.com/.well-known/matrix/server` 是否正常
2. 确认内部服务器在端口8443正常运行
3. 检查防火墙是否开放8443端口
4. 确认DNS解析正确指向内部服务器IP

---

**指南版本：** v1.0
**最后更新：** 2025-06-21 14:00:00
**适用场景：** Matrix服务栈混合部署架构的外部访问配置
