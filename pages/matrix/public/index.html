<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Matrix Server - niub.one</title>
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23368bd6'%3E%3Cpath d='M.632.55v22.9H2.28V24H0V0h2.28v.55zm7.043 7.26v1.157c.469-.698 1.235-1.047 2.297-1.047.802 0 1.427.312 1.875.937.469-.625 1.156-.937 2.063-.937 2.125 0 3.188 1.312 3.188 3.937v4.14h-1.329v-4.14c0-1.553-.802-2.329-2.406-2.329-.906 0-1.594.375-2.063 1.125v5.344h-1.329v-4.14c0-1.553-.802-2.329-2.406-2.329-.906 0-1.594.375-2.063 1.125v5.344H3.204V7.81h1.47zm14.063-.469c1.594 0 2.391.844 2.391 2.531v4.578h-1.47v-4.578c0-.844-.375-1.266-1.125-1.266-.75 0-1.125.422-1.125 1.266v4.578h-1.47V7.81h1.47v.531zm2.672 0h1.47v7.109h-1.47V7.81z'/%3E%3C/svg%3E">
    <style>
        /* Matrix.org official color scheme */
        :root {
            --matrix-green: #0DBD8B;
            --matrix-dark-bg: #0F1419;
            --matrix-card-bg: #1A1D23;
            --matrix-text-primary: #FFFFFF;
            --matrix-text-secondary: #8E99A4;
            --matrix-border: #2D3748;
            --matrix-hover: #2A2F3A;
            --matrix-accent: #0DBD8B;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background: var(--matrix-dark-bg);
            color: var(--matrix-text-primary);
            line-height: 1.6;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: var(--matrix-card-bg);
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            border: 1px solid var(--matrix-border);
            max-width: 600px;
            width: 100%;
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #0F1419, var(--matrix-green));
            color: white;
            padding: 40px 30px;
            text-align: center;
            border-bottom: 1px solid var(--matrix-border);
        }

        .logo {
            width: 60px;
            height: 60px;
            margin: 0 auto 20px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: bold;
        }

        .header h1 {
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .header p {
            opacity: 0.9;
            font-size: 16px;
        }

        .content {
            padding: 30px;
        }

        .services {
            display: grid;
            gap: 16px;
            margin-bottom: 30px;
        }

        .service-card {
            display: flex;
            align-items: center;
            padding: 20px;
            border: 1px solid var(--matrix-border);
            border-radius: 8px;
            text-decoration: none;
            color: var(--matrix-text-primary);
            transition: all 0.2s ease;
            background: var(--matrix-card-bg);
        }

        .service-card:hover {
            background: var(--matrix-hover);
            border-color: var(--matrix-green);
            transform: translateY(-1px);
            box-shadow: 0 4px 16px rgba(13, 189, 139, 0.2);
        }

        .service-icon {
            width: 48px;
            height: 48px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            margin-right: 16px;
            flex-shrink: 0;
        }

        .service-icon.synapse {
            background: linear-gradient(135deg, var(--matrix-green), #26d0a4);
            color: white;
        }

        .service-icon.element {
            background: linear-gradient(135deg, var(--matrix-green), #0F1419);
            color: white;
        }

        .service-icon.auth {
            background: linear-gradient(135deg, #2D3748, var(--matrix-green));
            color: white;
        }

        .service-icon.rtc {
            background: linear-gradient(135deg, var(--matrix-green), #2D3748);
            color: white;
        }

        .service-icon.federation {
            background: linear-gradient(135deg, #0F1419, var(--matrix-green));
            color: white;
        }

        .service-info h3 {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 4px;
            color: var(--matrix-text-primary);
        }

        .service-info p {
            font-size: 14px;
            color: var(--matrix-text-secondary);
        }

        .info-section {
            background: var(--matrix-hover);
            border: 1px solid var(--matrix-border);
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .info-section h3 {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 12px;
            color: var(--matrix-text-primary);
            display: flex;
            align-items: center;
        }

        .info-section h3::before {
            content: "ℹ️";
            margin-right: 8px;
        }

        .info-section p {
            font-size: 14px;
            color: var(--matrix-text-secondary);
            margin-bottom: 8px;
        }

        .info-section code {
            background: rgba(13, 189, 139, 0.15);
            color: var(--matrix-green);
            padding: 2px 6px;
            border-radius: 4px;
            font-family: "SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas, "Courier New", monospace;
            font-size: 13px;
        }

        .status {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 12px;
            background: rgba(13, 189, 139, 0.15);
            border: 1px solid rgba(13, 189, 139, 0.3);
            border-radius: 6px;
            margin-bottom: 20px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            background: var(--matrix-green);
            border-radius: 50%;
            margin-right: 8px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .status-text {
            font-size: 14px;
            color: var(--matrix-green);
            font-weight: 500;
        }

        .footer {
            text-align: center;
            padding: 20px 30px;
            border-top: 1px solid var(--matrix-border);
            background: var(--matrix-hover);
        }

        .footer p {
            font-size: 12px;
            color: var(--matrix-text-secondary);
        }

        @media (max-width: 480px) {
            .container {
                margin: 10px;
            }
            
            .header {
                padding: 30px 20px;
            }
            
            .content {
                padding: 20px;
            }
            
            .service-card {
                padding: 16px;
            }
            
            .service-icon {
                width: 40px;
                height: 40px;
                font-size: 18px;
                margin-right: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">M</div>
            <h1>Matrix Server</h1>
            <p>niub.one</p>
        </div>
        
        <div class="content">
            <div class="status">
                <div class="status-dot"></div>
                <div class="status-text">All services operational</div>
            </div>
            
            <div class="services">
                <a href="https://matrix.niub.one:8443" class="service-card">
                    <div class="service-icon synapse">🏠</div>
                    <div class="service-info">
                        <h3>Matrix Server</h3>
                        <p>Synapse homeserver for secure messaging</p>
                    </div>
                </a>
                
                <a href="https://chat.niub.one:8443" class="service-card">
                    <div class="service-icon element">💬</div>
                    <div class="service-info">
                        <h3>Element Web</h3>
                        <p>Web client for Matrix messaging</p>
                    </div>
                </a>
                
                <a href="https://mas.niub.one:8443" class="service-card">
                    <div class="service-icon auth">👤</div>
                    <div class="service-info">
                        <h3>Account Management</h3>
                        <p>Matrix Authentication Service</p>
                    </div>
                </a>
                
                <a href="https://rtc.niub.one:8443" class="service-card">
                    <div class="service-icon rtc">📞</div>
                    <div class="service-info">
                        <h3>Voice & Video</h3>
                        <p>Matrix RTC for calls and conferences</p>
                    </div>
                </a>

                <div class="service-card" style="cursor: default; opacity: 0.9;">
                    <div class="service-icon federation">🌐</div>
                    <div class="service-info">
                        <h3>Federation Status</h3>
                        <p>Connected to Matrix network - Can communicate with other servers</p>
                    </div>
                </div>
            </div>
            
            <div class="info-section">
                <h3>Connect with Matrix clients</h3>
                <p>To connect using any Matrix client:</p>
                <p>1. Download a Matrix client (Element, FluffyChat, etc.)</p>
                <p>2. Choose "Custom server" during setup</p>
                <p>3. Enter server address: <code>niub.one</code></p>
                <p>4. The client will automatically discover the server configuration</p>
            </div>
            
            <div class="info-section">
                <h3>Federation & Discovery</h3>
                <p><strong>Server Name:</strong> niub.one</p>
                <p><strong>Federation Port:</strong> {{FEDERATION_PORT:-8448}}</p>
                <p><strong>Client Port:</strong> 8443</p>
                <p><strong>Server Discovery:</strong> <code>niub.one/.well-known/matrix/server</code></p>
                <p><strong>Client Discovery:</strong> <code>niub.one/.well-known/matrix/client</code></p>
                <p><strong>Federation:</strong> ✅ Enabled - Can communicate with other Matrix servers</p>
            </div>

            <div class="info-section">
                <h3>Server Information</h3>
                <p><strong>Homeserver:</strong> matrix.niub.one:8443</p>
                <p><strong>Identity Server:</strong> mas.niub.one:8443</p>
                <p><strong>Registration:</strong> Contact administrator</p>
                <p><strong>Room Directory:</strong> Public rooms available</p>
            </div>
        </div>
        
        <div class="footer">
            <p>Powered by Matrix • Self-hosted with ❤️</p>
        </div>
    </div>
</body>
</html>