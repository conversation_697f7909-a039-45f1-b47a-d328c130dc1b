# Cloudflare Pages 部署说明
**用途：** Matrix服务栈外部重定向服务部署  
**平台：** Cloudflare Pages  
**类型：** 静态页面 + 重定向规则

## 1. 部署概述

### 1.1 为什么选择Cloudflare Pages？
- **免费额度充足**：每月100,000次请求免费
- **全球CDN**：访问速度快，可用性高
- **自动HTTPS**：免费SSL证书，自动续期
- **简单部署**：支持Git集成和CLI部署
- **重定向功能**：原生支持_redirects文件

### 1.2 功能说明
外部服务器的作用是将标准端口80/443的访问重定向到内部服务器的自定义端口：

```
用户访问: https://matrix.example.com
重定向到: https://internal-server.com:8443
```

## 2. 使用方法

### 2.1 前置要求
- Node.js 16+ 和 npm
- Cloudflare账户
- 域名托管在Cloudflare（或可以修改DNS）

### 2.2 一键部署
```bash
# 下载部署脚本
curl -fsSL https://your-repo.com/cloudflare-pages-deploy.sh -o cloudflare-pages-deploy.sh
chmod +x cloudflare-pages-deploy.sh

# 执行部署
./cloudflare-pages-deploy.sh
```

### 2.3 交互式配置
脚本会提示输入以下信息：

```
请输入主域名 (如: example.com): example.com
请输入内部服务器的自定义HTTPS端口 [默认: 8443]: 8443
Synapse子域名 [默认: matrix]: matrix
Element Web子域名 [默认: chat]: chat
MAS子域名 [默认: account]: account
Matrix RTC子域名 [默认: mrtc]: mrtc
请输入Cloudflare账户ID: your-account-id
请输入项目名称 [默认: matrix-redirect]: matrix-redirect
请输入内部服务器的公网IP或域名: your-internal-server.com
```

## 3. 生成的文件结构

### 3.1 Pages目录结构
```
pages-build/public/
├── index.html              # 主页面，显示服务列表
├── _redirects              # Cloudflare Pages重定向规则
└── .well-known/
    └── matrix/
        ├── server          # Matrix服务器发现
        └── client          # Matrix客户端发现
```

### 3.2 重定向规则示例
```
# _redirects文件内容
https://matrix.example.com/* https://internal-server.com:8443/:splat 301!
https://chat.example.com/* https://internal-server.com:8443/:splat 301!
https://account.example.com/* https://internal-server.com:8443/:splat 301!
https://mrtc.example.com/* https://internal-server.com:8443/:splat 301!
```

### 3.3 Matrix发现文件
```json
// .well-known/matrix/server
{
    "m.server": "matrix.example.com:8443"
}

// .well-known/matrix/client  
{
    "m.homeserver": {
        "base_url": "https://matrix.example.com:8443"
    },
    "m.identity_server": {
        "base_url": "https://account.example.com:8443"
    }
}
```

## 4. DNS配置

### 4.1 添加CNAME记录
在Cloudflare DNS中添加以下记录：

```
类型    名称      目标                        代理状态
CNAME   matrix    matrix-redirect.pages.dev   已代理
CNAME   chat      matrix-redirect.pages.dev   已代理  
CNAME   account   matrix-redirect.pages.dev   已代理
CNAME   mrtc      matrix-redirect.pages.dev   已代理
```

### 4.2 自定义域名配置
1. 在Cloudflare Pages项目设置中
2. 点击"Custom domains"
3. 添加自定义域名：
   - matrix.example.com
   - chat.example.com
   - account.example.com
   - mrtc.example.com

## 5. 验证部署

### 5.1 检查重定向
```bash
# 测试重定向是否正常
curl -I https://matrix.example.com
# 应该返回 301 重定向到内部服务器

curl -I https://chat.example.com  
# 应该返回 301 重定向到内部服务器
```

### 5.2 检查Matrix发现
```bash
# 测试Matrix服务器发现
curl https://example.com/.well-known/matrix/server
# 应该返回JSON格式的服务器信息

curl https://example.com/.well-known/matrix/client
# 应该返回JSON格式的客户端配置
```

### 5.3 访问测试
- 访问 https://matrix.example.com 应该重定向到内部服务器
- 访问 https://chat.example.com 应该重定向到内部服务器
- 访问 https://example.com 应该显示服务列表页面

## 6. 故障排除

### 6.1 常见问题

**问题1：重定向不生效**
- 检查_redirects文件格式是否正确
- 确认DNS记录已生效（可能需要等待几分钟）
- 检查Cloudflare代理状态是否开启

**问题2：SSL证书错误**
- 确认自定义域名已在Pages项目中配置
- 等待SSL证书自动颁发（通常几分钟内完成）

**问题3：Matrix发现失败**
- 检查.well-known文件是否正确生成
- 确认JSON格式无误
- 测试文件是否可以直接访问

### 6.2 调试命令
```bash
# 检查DNS解析
nslookup matrix.example.com

# 检查HTTP响应头
curl -I https://matrix.example.com

# 检查重定向链
curl -L -I https://matrix.example.com

# 测试Matrix发现
curl https://example.com/.well-known/matrix/server | jq
```

## 7. 更新和维护

### 7.1 更新配置
如需修改配置（如更换内部服务器地址），重新运行部署脚本即可：

```bash
./cloudflare-pages-deploy.sh
```

### 7.2 监控和日志
- 在Cloudflare Dashboard中查看Pages项目的访问统计
- 监控重定向请求的成功率
- 检查内部服务器的可用性

### 7.3 成本控制
- Cloudflare Pages免费额度：100,000次请求/月
- 超出免费额度后按使用量计费
- 可在Dashboard中设置使用量警报

## 8. 安全考虑

### 8.1 访问控制
- 重定向服务本身不需要特殊的访问控制
- 安全性主要依赖内部服务器的配置
- 可以在Cloudflare中配置WAF规则

### 8.2 DDoS防护
- Cloudflare提供免费的DDoS防护
- 可以配置速率限制规则
- 异常流量会被自动过滤

### 8.3 日志记录
- Cloudflare会记录所有访问日志
- 可以分析访问模式和异常行为
- 支持导出日志进行进一步分析

---

**文档版本：** v1.0  
**最后更新：** 2025-06-21 13:30:00  
**适用范围：** Matrix服务栈混合部署架构的外部重定向服务
